<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>句子成分的嵌套 - 理解从句中套从句的复杂结构</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            transition: transform 0.1s ease;
        }
        .card:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="bg-white">
<div class="p-6">
        
        <!-- 嵌套结构概述 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">句子成分的嵌套结构</h2>
            <p class="text-gray-700 mb-4 leading-relaxed">
                在英语中，从句可以嵌套在其他从句中，形成复杂的多层结构。这种嵌套可以发生在名词性从句、形容词性从句和副词性从句之间，创造出表达丰富、逻辑清晰的复合句。理解这些嵌套结构对于掌握高级英语语法至关重要。
            </p>
        </section>

        <!-- 基本嵌套类型 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">基本嵌套类型</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">名词从句嵌套</div>
                    <div class="keyword text-lg mb-1">I know that he said that he would come.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ noʊ ðæt hi sɛd ðæt hi wʊd kʌm/</div>
                    <div class="text-gray-700">我知道他说过他会来。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定语从句嵌套</div>
                    <div class="keyword text-lg mb-1">The book that I read that you recommended was great.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk ðæt aɪ rɛd ðæt ju ˌrɛkəˈmɛndəd wʌz greɪt/</div>
                    <div class="text-gray-700">你推荐的我读过的那本书很棒。</div>
                </div>
            </div>
        </section>

        <!-- 名词从句中的嵌套 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">名词从句中的嵌套结构</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                名词从句可以包含其他类型的从句，形成复杂的嵌套结构。最常见的是在宾语从句中嵌套定语从句或状语从句。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">宾语从句+定语从句</div>
                    <div class="keyword text-lg mb-1">She believes that the man who called yesterday is her brother.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi bɪˈlivz ðæt ðə mæn hu kɔld ˈjɛstərˌdeɪ ɪz hər ˈbrʌðər/</div>
                    <div class="text-gray-700">她相信昨天打电话的那个人是她的兄弟。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主语从句+状语从句</div>
                    <div class="keyword text-lg mb-1">What he said when he was angry surprised everyone.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt hi sɛd wɛn hi wʌz ˈæŋgri sərˈpraɪzd ˈɛvriˌwʌn/</div>
                    <div class="text-gray-700">他生气时说的话让每个人都感到惊讶。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表语从句+定语从句</div>
                    <div class="keyword text-lg mb-1">The problem is that the solution which we proposed was rejected.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑbləm ɪz ðæt ðə səˈluʃən wɪʧ wi prəˈpoʊzd wʌz rɪˈʤɛktəd/</div>
                    <div class="text-gray-700">问题是我们提出的解决方案被拒绝了。</div>
                </div>
            </div>
        </section>

        <!-- 定语从句中的嵌套 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">定语从句中的嵌套结构</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                定语从句内部可以包含名词从句、状语从句或其他定语从句，形成多层修饰关系。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定语从句+宾语从句</div>
                    <div class="keyword text-lg mb-1">The teacher who said that we should study harder is right.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiʧər hu sɛd ðæt wi ʃʊd ˈstʌdi ˈhɑrdər ɪz raɪt/</div>
                    <div class="text-gray-700">说我们应该更努力学习的那位老师是对的。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定语从句+状语从句</div>
                    <div class="keyword text-lg mb-1">The house that was built when my father was young is still standing.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə haʊs ðæt wʌz bɪlt wɛn maɪ ˈfɑðər wʌz jʌŋ ɪz stɪl ˈstændɪŋ/</div>
                    <div class="text-gray-700">我父亲年轻时建造的那座房子仍然矗立着。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多重定语从句</div>
                    <div class="keyword text-lg mb-1">The book that I bought that you recommended is excellent.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk ðæt aɪ bɔt ðæt ju ˌrɛkəˈmɛndəd ɪz ˈɛksələnt/</div>
                    <div class="text-gray-700">我买的你推荐的那本书非常棒。</div>
                </div>
            </div>
        </section>

        <!-- 状语从句中的嵌套 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">状语从句中的嵌套结构</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                状语从句可以包含名词从句或定语从句，为主句提供更详细的时间、原因、条件等信息。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间状语从句+定语从句</div>
                    <div class="keyword text-lg mb-1">When the man who lives next door came home, I was sleeping.</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn ðə mæn hu lɪvz nɛkst dɔr keɪm hoʊm, aɪ wʌz ˈslipɪŋ/</div>
                    <div class="text-gray-700">当住在隔壁的那个人回家时，我正在睡觉。</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">原因状语从句+宾语从句</div>
                    <div class="keyword text-lg mb-1">Because he knew that she was coming, he prepared dinner.</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪˈkɔz hi nu ðæt ʃi wʌz ˈkʌmɪŋ, hi prɪˈpɛrd ˈdɪnər/</div>
                    <div class="text-gray-700">因为他知道她要来，所以他准备了晚餐。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">条件状语从句+定语从句</div>
                    <div class="keyword text-lg mb-1">If the plan that we discussed works, we'll be successful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ðə plæn ðæt wi dɪˈskʌst wərks, wil bi səkˈsɛsfəl/</div>
                    <div class="text-gray-700">如果我们讨论的计划有效，我们就会成功。</div>
                </div>
            </div>
        </section>

        <!-- 三层嵌套结构 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">三层嵌套结构</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                在复杂的学术或文学语境中，句子可能包含三层或更多层的嵌套结构。这些结构需要仔细分析才能理解其完整含义。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">三层嵌套示例1</div>
                    <div class="keyword text-lg mb-1">I believe that the book that he wrote when he was young is valuable.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ bɪˈliv ðæt ðə bʊk ðæt hi roʊt wɛn hi wʌz jʌŋ ɪz ˈvæljəbəl/</div>
                    <div class="text-gray-700">我相信他年轻时写的那本书很有价值。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">三层嵌套示例2</div>
                    <div class="keyword text-lg mb-1">She told me that the reason why she left was that she felt unhappy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi toʊld mi ðæt ðə ˈrizən waɪ ʃi lɛft wʌz ðæt ʃi fɛlt ʌnˈhæpi/</div>
                    <div class="text-gray-700">她告诉我她离开的原因是她感到不快乐。</div>
                </div>
            </div>
        </section>

        <!-- 嵌套结构的分析方法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">嵌套结构的分析方法</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                分析复杂嵌套结构时，需要逐层剥离，找出主句和各层从句的关系。建议采用"由外到内"的分析方法。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">分析步骤示例</div>
                    <div class="keyword text-lg mb-1">The fact that he said that he would help us surprised me.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə fækt ðæt hi sɛd ðæt hi wʊd hɛlp ʌs sərˈpraɪzd mi/</div>
                    <div class="text-gray-700">他说他会帮助我们这个事实让我惊讶。</div>
                </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <h4 class="font-semibold mb-2 text-gray-800">分析步骤：</h4>
                <ol class="list-decimal list-inside space-y-2 text-gray-700">
                    <li><span class="keyword">主句</span>：The fact ... surprised me（某个事实让我惊讶）</li>
                    <li><span class="keyword">第一层从句</span>：that he said that he would help us（同位语从句，解释"fact"）</li>
                    <li><span class="keyword">第二层从句</span>：that he would help us（宾语从句，作"said"的宾语）</li>
                </ol>
            </div>
        </section>

        <!-- 复杂嵌套结构实例 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">复杂嵌套结构实例</h3>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术写作风格</div>
                    <div class="keyword text-lg mb-1">The research that we conducted shows that students who study regularly perform better than those who don't.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈsərʧ ðæt wi kənˈdʌktəd ʃoʊz ðæt ˈstudənts hu ˈstʌdi ˈrɛgjələrli pərˈfɔrm ˈbɛtər ðæn ðoʊz hu doʊnt/</div>
                    <div class="text-gray-700">我们进行的研究表明，定期学习的学生比不定期学习的学生表现更好。</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">新闻报道风格</div>
                    <div class="keyword text-lg mb-1">Officials announced that the project that was delayed because of weather will resume next month.</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈfɪʃəlz əˈnaʊnst ðæt ðə ˈprɑʤɛkt ðæt wʌz dɪˈleɪd bɪˈkɔz ʌv ˈwɛðər wɪl rɪˈzum nɛkst mʌnθ/</div>
                    <div class="text-gray-700">官员宣布因天气延误的项目将在下个月恢复。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">文学描述风格</div>
                    <div class="keyword text-lg mb-1">The old man who lived alone remembered the days when he was young and happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə oʊld mæn hu lɪvd əˈloʊn rɪˈmɛmbərd ðə deɪz wɛn hi wʌz jʌŋ ænd ˈhæpi/</div>
                    <div class="text-gray-700">独自生活的老人回忆起他年轻快乐的日子。</div>
                </div>
            </div>
        </section>

        <!-- 嵌套结构中的关系词 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">嵌套结构中的关系词</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                在嵌套结构中，不同的关系词起着连接各层从句的重要作用。理解这些关系词的功能有助于准确理解句子结构。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">名词从句引导词</div>
                    <div class="keyword text-lg mb-1">that, what, who, when, where, why, how</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæt, wʌt, hu, wɛn, wɛr, waɪ, haʊ/</div>
                    <div class="text-gray-700">引导主语、宾语、表语、同位语从句</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定语从句引导词</div>
                    <div class="keyword text-lg mb-1">who, whom, whose, which, that, when, where</div>
                    <div class="text-sm text-gray-600 mb-1">/hu, hum, huz, wɪʧ, ðæt, wɛn, wɛr/</div>
                    <div class="text-gray-700">修饰名词或代词</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状语从句引导词</div>
                    <div class="keyword text-lg mb-1">when, while, because, if, although, since</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn, waɪl, bɪˈkɔz, ɪf, ɔlˈðoʊ, sɪns/</div>
                    <div class="text-gray-700">表示时间、原因、条件、让步等</div>
                </div>
            </div>
        </section>

        <!-- 常见的嵌套模式 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常见的嵌套模式</h3>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主语从句+定语从句模式</div>
                    <div class="keyword text-lg mb-1">What the teacher who came yesterday said was important.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ðə ˈtiʧər hu keɪm ˈjɛstərˌdeɪ sɛd wʌz ɪmˈpɔrtənt/</div>
                    <div class="text-gray-700">昨天来的那位老师说的话很重要。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">宾语从句+状语从句模式</div>
                    <div class="keyword text-lg mb-1">He told me that he would call when he arrived home.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi toʊld mi ðæt hi wʊd kɔl wɛn hi əˈraɪvd hoʊm/</div>
                    <div class="text-gray-700">他告诉我他到家后会打电话。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定语从句+宾语从句模式</div>
                    <div class="keyword text-lg mb-1">The person who said that he knew the answer was wrong.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈpərsən hu sɛd ðæt hi nu ði ˈænsər wʌz rɔŋ/</div>
                    <div class="text-gray-700">说他知道答案的那个人错了。</div>
                </div>
            </div>
        </section>

        <!-- 理解嵌套结构的技巧 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">理解嵌套结构的技巧</h3>

            <div class="bg-blue-50 p-4 rounded-lg mb-6">
                <h4 class="font-semibold mb-3 text-gray-800">实用技巧：</h4>
                <ul class="space-y-2 text-gray-700">
                    <li>• <span class="keyword">标记关系词</span>：首先找出所有的关系词（that, which, who, when等）</li>
                    <li>• <span class="keyword">识别主句</span>：找到句子的主要主语和谓语</li>
                    <li>• <span class="keyword">逐层分析</span>：从最外层开始，逐步分析每一层从句</li>
                    <li>• <span class="keyword">理解逻辑关系</span>：明确各从句之间的逻辑关系</li>
                    <li>• <span class="keyword">重新组织</span>：必要时可以将复杂句子拆分成简单句来理解</li>
                </ul>
            </div>
        </section>

        <!-- 高级嵌套结构实例 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">高级嵌套结构实例</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                在正式写作和学术语境中，经常出现更复杂的嵌套结构。这些结构虽然复杂，但遵循相同的语法规则。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">四层嵌套结构</div>
                    <div class="keyword text-lg mb-1">The idea that the book that he wrote when he was sick contains wisdom that we need is fascinating.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði aɪˈdiə ðæt ðə bʊk ðæt hi roʊt wɛn hi wʌz sɪk kənˈteɪnz ˈwɪzdəm ðæt wi nid ɪz ˈfæsəˌneɪtɪŋ/</div>
                    <div class="text-gray-700">他生病时写的书包含我们需要的智慧这个想法很迷人。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合嵌套结构</div>
                    <div class="keyword text-lg mb-1">Although the plan that we discussed yesterday seems good, I think that we should consider what might happen if it fails.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ ðə plæn ðæt wi dɪˈskʌst ˈjɛstərˌdeɪ simz gʊd, aɪ θɪŋk ðæt wi ʃʊd kənˈsɪdər wʌt maɪt ˈhæpən ɪf ɪt feɪlz/</div>
                    <div class="text-gray-700">虽然我们昨天讨论的计划看起来不错，但我认为我们应该考虑如果失败会发生什么。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术论文风格</div>
                    <div class="keyword text-lg mb-1">The study reveals that students who participate in activities that require critical thinking develop skills that help them succeed in college.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi rɪˈvilz ðæt ˈstudənts hu pɑrˈtɪsəˌpeɪt ɪn ækˈtɪvətiz ðæt rɪˈkwaɪər ˈkrɪtɪkəl ˈθɪŋkɪŋ dɪˈvɛləp skɪlz ðæt hɛlp ðɛm səkˈsid ɪn ˈkɑlɪʤ/</div>
                    <div class="text-gray-700">研究表明，参与需要批判性思维活动的学生会培养帮助他们在大学成功的技能。</div>
                </div>
            </div>
        </section>

        <!-- 嵌套结构中的省略现象 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">嵌套结构中的省略现象</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                在嵌套结构中，为了避免重复和使句子更简洁，经常会出现省略现象。理解这些省略有助于更好地理解复杂句子。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">关系代词省略</div>
                    <div class="keyword text-lg mb-1">The book (that) I read (that) you recommended was excellent.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk aɪ rɛd ju ˌrɛkəˈmɛndəd wʌz ˈɛksələnt/</div>
                    <div class="text-gray-700">我读的你推荐的那本书很棒。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">连词that省略</div>
                    <div class="keyword text-lg mb-1">I believe (that) the plan (that) we made will work.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ bɪˈliv ðə plæn wi meɪd wɪl wərk/</div>
                    <div class="text-gray-700">我相信我们制定的计划会成功。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主语省略</div>
                    <div class="keyword text-lg mb-1">When (he was) young, he believed (that) he could change the world.</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn jʌŋ, hi bɪˈlivd hi kʊd ʧeɪnʤ ðə wərld/</div>
                    <div class="text-gray-700">年轻时，他相信他能改变世界。</div>
                </div>
            </div>
        </section>

        <!-- 嵌套结构的语序问题 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">嵌套结构的语序问题</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                在复杂嵌套结构中，语序的安排对句子的清晰度至关重要。不当的语序可能导致歧义或理解困难。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">清晰的语序</div>
                    <div class="keyword text-lg mb-1">The teacher explained that the students who studied hard would pass the exam.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiʧər ɪkˈspleɪnd ðæt ðə ˈstudənts hu ˈstʌdid hɑrd wʊd pæs ði ɪgˈzæm/</div>
                    <div class="text-gray-700">老师解释说努力学习的学生会通过考试。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">可能产生歧义的语序</div>
                    <div class="keyword text-lg mb-1">The students that the teacher said would pass studied hard.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstudənts ðæt ðə ˈtiʧər sɛd wʊd pæs ˈstʌdid hɑrd/</div>
                    <div class="text-gray-700">老师说会通过的学生努力学习了。</div>
                </div>
            </div>
        </section>

        <!-- 实际应用场景 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">实际应用场景</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                嵌套结构在不同的语境中有不同的应用。了解这些应用场景有助于在适当的情况下使用复杂结构。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">商务写作</div>
                    <div class="keyword text-lg mb-1">The proposal that our team submitted that addresses the issues you mentioned has been approved.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə prəˈpoʊzəl ðæt aʊər tim səbˈmɪtəd ðæt əˈdrɛsəz ði ˈɪʃuz ju ˈmɛnʃənd hæz bin əˈpruvd/</div>
                    <div class="text-gray-700">我们团队提交的解决您提到问题的提案已被批准。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">技术文档</div>
                    <div class="keyword text-lg mb-1">The system that we developed ensures that data that users input is processed securely.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈsɪstəm ðæt wi dɪˈvɛləpt ɪnˈʃʊrz ðæt ˈdeɪtə ðæt ˈjuzərz ˈɪnˌpʊt ɪz ˈprɑsɛst sɪˈkjʊrli/</div>
                    <div class="text-gray-700">我们开发的系统确保用户输入的数据得到安全处理。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">法律文件</div>
                    <div class="keyword text-lg mb-1">The contract states that the party that fails to meet obligations that are specified herein shall pay damages.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑntrækt steɪts ðæt ðə ˈpɑrti ðæt feɪlz tu mit ˌɑbləˈgeɪʃənz ðæt ɑr ˈspɛsəˌfaɪd hɪrˈɪn ʃæl peɪ ˈdæməʤəz/</div>
                    <div class="text-gray-700">合同规定未能履行此处规定义务的一方应支付损害赔偿。</div>
                </div>
            </div>
        </section>

        <!-- 避免过度复杂化 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">避免过度复杂化</h3>
            <p class="text-gray-700 mb-4 leading-relaxed">
                虽然嵌套结构可以表达复杂的思想，但过度使用会影响理解。在实际写作中，应该平衡复杂性和清晰度。
            </p>

            <div class="bg-amber-50 p-4 rounded-lg mb-6">
                <h4 class="font-semibold mb-3 text-gray-800">写作建议：</h4>
                <ul class="space-y-2 text-gray-700">
                    <li>• <span class="keyword">适度使用</span>：不要在一个句子中嵌套过多层次</li>
                    <li>• <span class="keyword">保持清晰</span>：确保读者能够理解句子的主要意思</li>
                    <li>• <span class="keyword">考虑拆分</span>：必要时将复杂句子拆分成多个简单句</li>
                    <li>• <span class="keyword">使用标点</span>：适当使用逗号和其他标点符号来分隔从句</li>
                    <li>• <span class="keyword">检查逻辑</span>：确保各从句之间的逻辑关系清晰</li>
                </ul>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">过度复杂的例子</div>
                    <div class="keyword text-lg mb-1">The book that the author who lived in the house that was built when my grandfather was young wrote is interesting.</div>
                    <div class="text-sm text-gray-600 mb-1">（过于复杂，难以理解）</div>
                    <div class="text-gray-700">住在我祖父年轻时建造的房子里的作者写的书很有趣。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">改进后的版本</div>
                    <div class="keyword text-lg mb-1">The author lived in an old house that was built when my grandfather was young. The book he wrote is very interesting.</div>
                    <div class="text-sm text-gray-600 mb-1">（清晰易懂）</div>
                    <div class="text-gray-700">作者住在我祖父年轻时建造的老房子里。他写的书很有趣。</div>
                </div>
            </div>
        </section>

    </div>
</body>
</html>
