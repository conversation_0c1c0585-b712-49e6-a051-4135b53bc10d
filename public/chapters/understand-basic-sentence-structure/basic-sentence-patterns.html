<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本句型</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }

        .card {
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-white">
<div class="p-6">
    <!-- 1. 陈述句（Declarative Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">1. 陈述句（Declarative Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">陈述句是用来陈述事实、表达观点或描述情况的句子。它是最常用的句型，通常以句号结尾。陈述句的基本语序是：主语
            + 谓语 + 宾语/表语。</p>

        <!-- 1.1 基本结构 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">1.1 基本结构</h3>

            <!-- 主谓结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主谓结构（Subject + Verb）</h4>
                <p class="text-gray-600 mb-4">最简单的陈述句结构，只包含主语和谓语动词。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基础句型</div>
                        <div class="keyword text-lg mb-1">Birds fly.</div>
                        <div class="text-sm text-gray-600 mb-1">/bərdz flaɪ/</div>
                        <div class="text-gray-700">鸟儿飞翔。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">人称主语</div>
                        <div class="keyword text-lg mb-1">She sings.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi sɪŋz/</div>
                        <div class="text-gray-700">她唱歌。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复数主语</div>
                        <div class="keyword text-lg mb-1">Children play.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtʃɪldrən pleɪ/</div>
                        <div class="text-gray-700">孩子们玩耍。</div>
                    </div>
                </div>
            </div>

            <!-- 主谓宾结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主谓宾结构（Subject + Verb + Object）</h4>
                <p class="text-gray-600 mb-4">包含主语、及物动词和宾语的完整结构。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">动作对象</div>
                        <div class="keyword text-lg mb-1">I read books.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ rid bʊks/</div>
                        <div class="text-gray-700">我读书。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">具体宾语</div>
                        <div class="keyword text-lg mb-1">She drinks coffee.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi drɪŋks ˈkɔfi/</div>
                        <div class="text-gray-700">她喝咖啡。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">人物宾语</div>
                        <div class="keyword text-lg mb-1">We visit our friends.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi ˈvɪzɪt aʊr frɛndz/</div>
                        <div class="text-gray-700">我们拜访朋友。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">抽象宾语</div>
                        <div class="keyword text-lg mb-1">He loves music.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi lʌvz ˈmjuzɪk/</div>
                        <div class="text-gray-700">他喜欢音乐。</div>
                    </div>
                </div>
            </div>

            <!-- 主系表结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主系表结构（Subject + Linking Verb +
                    Complement）</h4>
                <p class="text-gray-600 mb-4">使用系动词连接主语和表语，描述主语的状态或特征。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">be动词 + 形容词</div>
                        <div class="keyword text-lg mb-1">The weather is beautiful.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈwɛðər ɪz ˈbjutəfəl/</div>
                        <div class="text-gray-700">天气很美丽。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">be动词 + 名词</div>
                        <div class="keyword text-lg mb-1">She is a doctor.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ə ˈdɑktər/</div>
                        <div class="text-gray-700">她是一名医生。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">感官动词</div>
                        <div class="keyword text-lg mb-1">The food tastes delicious.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə fud teɪsts dɪˈlɪʃəs/</div>
                        <div class="text-gray-700">食物很美味。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">变化动词</div>
                        <div class="keyword text-lg mb-1">He becomes tired.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi bɪˈkʌmz ˈtaɪərd/</div>
                        <div class="text-gray-700">他变得疲倦。</div>
                    </div>
                </div>
            </div>

            <!-- 主谓双宾结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主谓双宾结构（Subject + Verb + Indirect Object +
                    Direct Object）</h4>
                <p class="text-gray-600 mb-4">包含间接宾语和直接宾语的结构，通常表示给予或传递的动作。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">给予动作</div>
                        <div class="keyword text-lg mb-1">I gave him a book.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ɡeɪv hɪm ə bʊk/</div>
                        <div class="text-gray-700">我给了他一本书。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">展示动作</div>
                        <div class="keyword text-lg mb-1">She showed me her photos.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ʃoʊd mi hər ˈfoʊtoʊz/</div>
                        <div class="text-gray-700">她给我看了她的照片。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">传递信息</div>
                        <div class="keyword text-lg mb-1">He told us the story.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi toʊld ʌs ðə ˈstɔri/</div>
                        <div class="text-gray-700">他给我们讲了这个故事。</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 1.2 时态变化 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">1.2 时态变化</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般现在时</div>
                    <div class="keyword text-lg mb-1">She works every day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wərks ˈɛvri deɪ/</div>
                    <div class="text-gray-700">她每天工作。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般过去时</div>
                    <div class="keyword text-lg mb-1">I visited Paris last year.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈvɪzɪtəd ˈpɛrɪs læst jɪr/</div>
                    <div class="text-gray-700">我去年访问了巴黎。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般将来时</div>
                    <div class="keyword text-lg mb-1">We will meet tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi wɪl mit təˈmɑroʊ/</div>
                    <div class="text-gray-700">我们明天会见面。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 2. 疑问句（Interrogative Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">2. 疑问句（Interrogative Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">
            疑问句用来提出问题，获取信息。疑问句通常以问号结尾，语调上扬。根据结构和功能，疑问句分为四种基本类型。</p>

        <!-- 2.1 一般疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.1 一般疑问句（Yes/No Questions）</h3>
            <p class="text-gray-600 mb-4">用来询问是否的问题，答案通常是yes或no。结构是：助动词/be动词/情态动词 + 主语 +
                其他成分？</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">be动词提前</div>
                    <div class="keyword text-lg mb-1">Are you a student?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑr ju ə ˈstudənt/</div>
                    <div class="text-gray-700">你是学生吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">助动词do</div>
                    <div class="keyword text-lg mb-1">Do you like pizza?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju laɪk ˈpitsə/</div>
                    <div class="text-gray-700">你喜欢披萨吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">助动词does</div>
                    <div class="keyword text-lg mb-1">Does she speak English?</div>
                    <div class="text-sm text-gray-600 mb-1">/dʌz ʃi spik ˈɪŋɡlɪʃ/</div>
                    <div class="text-gray-700">她说英语吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情态动词</div>
                    <div class="keyword text-lg mb-1">Can you help me?</div>
                    <div class="text-sm text-gray-600 mb-1">/kæn ju hɛlp mi/</div>
                    <div class="text-gray-700">你能帮助我吗？</div>
                </div>
            </div>
        </div>

        <!-- 2.2 特殊疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.2 特殊疑问句（Wh-Questions）</h3>
            <p class="text-gray-600 mb-4">以疑问词开头的问句，用来询问具体信息。结构是：疑问词 + 助动词/be动词 + 主语 +
                其他成分？</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">what询问事物</div>
                    <div class="keyword text-lg mb-1">What is your name?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ɪz jʊr neɪm/</div>
                    <div class="text-gray-700">你的名字是什么？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">where询问地点</div>
                    <div class="keyword text-lg mb-1">Where do you live?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛr du ju lɪv/</div>
                    <div class="text-gray-700">你住在哪里？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">when询问时间</div>
                    <div class="keyword text-lg mb-1">When will you come?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn wɪl ju kʌm/</div>
                    <div class="text-gray-700">你什么时候来？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">who询问人物</div>
                    <div class="keyword text-lg mb-1">Who is that man?</div>
                    <div class="text-sm text-gray-600 mb-1">/hu ɪz ðæt mæn/</div>
                    <div class="text-gray-700">那个人是谁？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">why询问原因</div>
                    <div class="keyword text-lg mb-1">Why are you late?</div>
                    <div class="text-sm text-gray-600 mb-1">/waɪ ɑr ju leɪt/</div>
                    <div class="text-gray-700">你为什么迟到？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">how询问方式</div>
                    <div class="keyword text-lg mb-1">How do you go to work?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ du ju ɡoʊ tu wərk/</div>
                    <div class="text-gray-700">你怎么去上班？</div>
                </div>
            </div>
        </div>

        <!-- 2.3 选择疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.3 选择疑问句（Alternative Questions）</h3>
            <p class="text-gray-600 mb-4">提供两个或多个选项供选择，用or连接选项。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">两个选项</div>
                    <div class="keyword text-lg mb-1">Do you want tea or coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju wʌnt ti ɔr ˈkɔfi/</div>
                    <div class="text-gray-700">你想要茶还是咖啡？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多个选项</div>
                    <div class="keyword text-lg mb-1">Which color do you prefer, red, blue, or green?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪtʃ ˈkʌlər du ju prɪˈfər rɛd blu ɔr ɡrin/</div>
                    <div class="text-gray-700">你更喜欢哪种颜色，红色、蓝色还是绿色？</div>
                </div>
            </div>
        </div>

        <!-- 2.4 反意疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.4 反意疑问句（Tag Questions）</h3>
            <p class="text-gray-600 mb-4">
                在陈述句后面加上简短的疑问部分，用来确认信息或寻求同意。遵循"前肯后否，前否后肯"的原则。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">前肯后否</div>
                    <div class="keyword text-lg mb-1">You are a teacher, aren't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑr ə ˈtitʃər ˌɑrənt ju/</div>
                    <div class="text-gray-700">你是老师，对吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">前否后肯</div>
                    <div class="keyword text-lg mb-1">She doesn't like music, does she?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈdʌzənt laɪk ˈmjuzɪk dʌz ʃi/</div>
                    <div class="text-gray-700">她不喜欢音乐，对吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情态动词</div>
                    <div class="keyword text-lg mb-1">You can swim, can't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju kæn swɪm kænt ju/</div>
                    <div class="text-gray-700">你会游泳，对吗？</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 3. 祈使句（Imperative Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">3. 祈使句（Imperative Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">
            祈使句用来表达请求、命令、建议、邀请等。通常省略主语you，以动词原形开头，语调比较直接。</p>

        <!-- 3.1 肯定祈使句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">3.1 肯定祈使句</h3>
            <p class="text-gray-600 mb-4">直接以动词原形开头，表示要求对方做某事。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简单命令</div>
                    <div class="keyword text-lg mb-1">Sit down.</div>
                    <div class="text-sm text-gray-600 mb-1">/sɪt daʊn/</div>
                    <div class="text-gray-700">坐下。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">礼貌请求</div>
                    <div class="keyword text-lg mb-1">Please help me.</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz hɛlp mi/</div>
                    <div class="text-gray-700">请帮助我。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">具体指示</div>
                    <div class="keyword text-lg mb-1">Open the window.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊpən ðə ˈwɪndoʊ/</div>
                    <div class="text-gray-700">打开窗户。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动作建议</div>
                    <div class="keyword text-lg mb-1">Take a break.</div>
                    <div class="text-sm text-gray-600 mb-1">/teɪk ə breɪk/</div>
                    <div class="text-gray-700">休息一下。</div>
                </div>
            </div>
        </div>

        <!-- 3.2 否定祈使句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">3.2 否定祈使句</h3>
            <p class="text-gray-600 mb-4">用Don't + 动词原形来表示禁止或不要做某事。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简单禁止</div>
                    <div class="keyword text-lg mb-1">Don't smoke.</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt smoʊk/</div>
                    <div class="text-gray-700">不要吸烟。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">安全警告</div>
                    <div class="keyword text-lg mb-1">Don't touch that!</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt tʌtʃ ðæt/</div>
                    <div class="text-gray-700">不要碰那个！</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">礼貌提醒</div>
                    <div class="keyword text-lg mb-1">Please don't be late.</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz doʊnt bi leɪt/</div>
                    <div class="text-gray-700">请不要迟到。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">行为禁止</div>
                    <div class="keyword text-lg mb-1">Don't worry about it.</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt ˈwɜri əˈbaʊt ɪt/</div>
                    <div class="text-gray-700">不要担心这个。</div>
                </div>
            </div>
        </div>

        <!-- 3.3 Let's句型 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">3.3 Let's句型</h3>
            <p class="text-gray-600 mb-4">用Let's + 动词原形来表示建议或邀请一起做某事。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">共同行动</div>
                    <div class="keyword text-lg mb-1">Let's go to the park.</div>
                    <div class="text-sm text-gray-600 mb-1">/lets ɡoʊ tu ðə pɑrk/</div>
                    <div class="text-gray-700">我们去公园吧。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">活动建议</div>
                    <div class="keyword text-lg mb-1">Let's have dinner together.</div>
                    <div class="text-sm text-gray-600 mb-1">/lets hæv ˈdɪnər təˈɡeðər/</div>
                    <div class="text-gray-700">我们一起吃晚饭吧。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否定建议</div>
                    <div class="keyword text-lg mb-1">Let's not argue.</div>
                    <div class="text-sm text-gray-600 mb-1">/lets nɑt ˈɑrɡju/</div>
                    <div class="text-gray-700">我们不要争论。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开始行动</div>
                    <div class="keyword text-lg mb-1">Let's start the meeting.</div>
                    <div class="text-sm text-gray-600 mb-1">/lets stɑrt ðə ˈmitɪŋ/</div>
                    <div class="text-gray-700">我们开始会议吧。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 4. 感叹句（Exclamatory Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">4. 感叹句（Exclamatory Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">感叹句用来表达强烈的情感，如惊讶、兴奋、愤怒等。通常以what或how开头，以感叹号结尾。</p>

        <!-- 4.1 What引导的感叹句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">4.1 What引导的感叹句</h3>
            <p class="text-gray-600 mb-4">What + (a/an) + 形容词 + 名词 + 主语 + 谓语！</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形容词+可数名词单数</div>
                    <div class="keyword text-lg mb-1">What a beautiful day!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə ˈbjutəfəl deɪ/</div>
                    <div class="text-gray-700">多么美好的一天！</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形容词+可数名词复数</div>
                    <div class="keyword text-lg mb-1">What lovely flowers!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ˈlʌvli ˈflaʊərz/</div>
                    <div class="text-gray-700">多么可爱的花朵！</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形容词+不可数名词</div>
                    <div class="keyword text-lg mb-1">What terrible weather!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ˈterəbəl ˈweðər/</div>
                    <div class="text-gray-700">多么糟糕的天气！</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">惊讶感叹</div>
                    <div class="keyword text-lg mb-1">What a surprise!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə sərˈpraɪz/</div>
                    <div class="text-gray-700">真是个惊喜！</div>
                </div>
            </div>
        </div>

        <!-- 4.2 How引导的感叹句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">4.2 How引导的感叹句</h3>
            <p class="text-gray-600 mb-4">How + 形容词/副词 + 主语 + 谓语！</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">how + 形容词</div>
                    <div class="keyword text-lg mb-1">How beautiful she is!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ˈbjutəfəl ʃi ɪz/</div>
                    <div class="text-gray-700">她多么美丽！</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">how + 副词</div>
                    <div class="keyword text-lg mb-1">How quickly he runs!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ˈkwɪkli hi rʌnz/</div>
                    <div class="text-gray-700">他跑得多么快！</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情感表达</div>
                    <div class="keyword text-lg mb-1">How exciting!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ɪkˈsaɪtɪŋ/</div>
                    <div class="text-gray-700">多么令人兴奋！</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度强调</div>
                    <div class="keyword text-lg mb-1">How hard he works!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ hɑrd hi wɜrks/</div>
                    <div class="text-gray-700">他工作多么努力！</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 5. 复合句（Complex Sentences） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">5. 复合句（Complex Sentences）</h2>
        <p class="text-gray-600 mb-8 text-lg">复合句由一个主句和一个或多个从句组成。从句不能独立存在，需要依附于主句。</p>

        <!-- 5.1 名词性从句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">5.1 名词性从句</h3>
            <p class="text-gray-600 mb-4">名词性从句在句中充当名词的作用，可以作主语、宾语、表语或同位语。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主语从句</div>
                    <div class="keyword text-lg mb-1">What you said is true.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ju sed ɪz tru/</div>
                    <div class="text-gray-700">你说的是真的。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">宾语从句</div>
                    <div class="keyword text-lg mb-1">I know that he is coming.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ noʊ ðæt hi ɪz ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">我知道他要来。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表语从句</div>
                    <div class="keyword text-lg mb-1">The problem is that we need more time.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑbləm ɪz ðæt wi nid mɔr taɪm/</div>
                    <div class="text-gray-700">问题是我们需要更多时间。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">同位语从句</div>
                    <div class="keyword text-lg mb-1">The news that he won surprised us.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə nuz ðæt hi wʌn sərˈpraɪzd ʌs/</div>
                    <div class="text-gray-700">他获胜的消息让我们惊讶。</div>
                </div>
            </div>
        </div>

        <!-- 5.2 定语从句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">5.2 定语从句</h3>
            <p class="text-gray-600 mb-4">定语从句用来修饰名词或代词，由关系代词或关系副词引导。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">who指人作主语</div>
                    <div class="keyword text-lg mb-1">The man who is talking is my teacher.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə mæn hu ɪz ˈtɔkɪŋ ɪz maɪ ˈtitʃər/</div>
                    <div class="text-gray-700">正在说话的那个人是我的老师。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">which指物</div>
                    <div class="keyword text-lg mb-1">The book which I bought is interesting.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk wɪtʃ aɪ bɔt ɪz ˈɪntrəstɪŋ/</div>
                    <div class="text-gray-700">我买的那本书很有趣。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">that指人或物</div>
                    <div class="keyword text-lg mb-1">The car that he drives is new.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kɑr ðæt hi draɪvz ɪz nu/</div>
                    <div class="text-gray-700">他开的那辆车是新的。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">where指地点</div>
                    <div class="keyword text-lg mb-1">This is the place where we met.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðɪs ɪz ðə pleɪs wer wi met/</div>
                    <div class="text-gray-700">这是我们相遇的地方。</div>
                </div>
            </div>
        </div>

        <!-- 5.3 状语从句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">5.3 状语从句</h3>
            <p class="text-gray-600 mb-4">状语从句用来修饰动词、形容词或副词，表示时间、地点、原因、条件等。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间状语从句</div>
                    <div class="keyword text-lg mb-1">When he comes, I'll tell him.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen hi kʌmz aɪl tel hɪm/</div>
                    <div class="text-gray-700">当他来的时候，我会告诉他。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">原因状语从句</div>
                    <div class="keyword text-lg mb-1">Because it's raining, we stay home.</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪˈkɔz ɪts ˈreɪnɪŋ wi steɪ hoʊm/</div>
                    <div class="text-gray-700">因为下雨，我们待在家里。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">条件状语从句</div>
                    <div class="keyword text-lg mb-1">If you study hard, you'll pass.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ju ˈstʌdi hɑrd jul pæs/</div>
                    <div class="text-gray-700">如果你努力学习，你会通过的。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">让步状语从句</div>
                    <div class="keyword text-lg mb-1">Although it's cold, he goes out.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ ɪts koʊld hi ɡoʊz aʊt/</div>
                    <div class="text-gray-700">尽管很冷，他还是出去了。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">比较状语从句</div>
                    <div class="keyword text-lg mb-1">She runs faster than he does.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi rʌnz ˈfæstər ðæn hi dʌz/</div>
                    <div class="text-gray-700">她跑得比他快。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结果状语从句</div>
                    <div class="keyword text-lg mb-1">He worked so hard that he succeeded.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wɜrkt soʊ hɑrd ðæt hi səkˈsidəd/</div>
                    <div class="text-gray-700">他工作如此努力以至于成功了。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 6. 并列句（Compound Sentences） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">6. 并列句（Compound Sentences）</h2>
        <p class="text-gray-600 mb-8 text-lg">并列句由两个或多个独立的简单句通过并列连词连接而成，各分句地位平等。</p>

        <!-- 6.1 并列连词 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">6.1 常用并列连词</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">and表示并列</div>
                    <div class="keyword text-lg mb-1">I like tea, and she likes coffee.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ti ænd ʃi laɪks ˈkɔfi/</div>
                    <div class="text-gray-700">我喜欢茶，她喜欢咖啡。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">but表示转折</div>
                    <div class="keyword text-lg mb-1">He is rich, but he is not happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz rɪtʃ bʌt hi ɪz nɑt ˈhæpi/</div>
                    <div class="text-gray-700">他很富有，但他不快乐。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">or表示选择</div>
                    <div class="keyword text-lg mb-1">You can walk, or you can take the bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/ju kæn wɔk ɔr ju kæn teɪk ðə bʌs/</div>
                    <div class="text-gray-700">你可以走路，或者坐公交车。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">so表示结果</div>
                    <div class="keyword text-lg mb-1">It was late, so we went home.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wʌz leɪt soʊ wi went hoʊm/</div>
                    <div class="text-gray-700">天晚了，所以我们回家了。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">for表示原因</div>
                    <div class="keyword text-lg mb-1">We stayed inside, for it was raining.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi steɪd ɪnˈsaɪd fɔr ɪt wʌz ˈreɪnɪŋ/</div>
                    <div class="text-gray-700">我们待在里面，因为在下雨。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">yet表示转折</div>
                    <div class="keyword text-lg mb-1">He studied hard, yet he failed.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈstʌdid hɑrd jet hi feɪld/</div>
                    <div class="text-gray-700">他努力学习，但还是失败了。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 7. 语序变化与特殊句型 -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">7. 语序变化与特殊句型</h2>
        <p class="text-gray-600 mb-8 text-lg">英语的基本语序是SVO（主-谓-宾），但在某些情况下会发生语序变化。</p>

        <!-- 7.1 倒装句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">7.1 倒装句</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">There be句型</div>
                    <div class="keyword text-lg mb-1">There are many books on the table.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer ɑr ˈmeni bʊks ɑn ðə ˈteɪbəl/</div>
                    <div class="text-gray-700">桌子上有很多书。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Here/There开头</div>
                    <div class="keyword text-lg mb-1">Here comes the bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/hɪr kʌmz ðə bʌs/</div>
                    <div class="text-gray-700">公交车来了。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否定词开头</div>
                    <div class="keyword text-lg mb-1">Never have I seen such a thing.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnevər hæv aɪ sin sʌtʃ ə θɪŋ/</div>
                    <div class="text-gray-700">我从未见过这样的事。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Only开头</div>
                    <div class="keyword text-lg mb-1">Only then did I understand.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊnli ðen dɪd aɪ ˌʌndərˈstænd/</div>
                    <div class="text-gray-700">只有那时我才明白。</div>
                </div>
            </div>
        </div>

        <!-- 7.2 强调句型 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">7.2 强调句型</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">It is...that强调主语</div>
                    <div class="keyword text-lg mb-1">It is Tom that broke the window.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt ɪz tɑm ðæt broʊk ðə ˈwɪndoʊ/</div>
                    <div class="text-gray-700">是汤姆打破了窗户。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">It is...that强调宾语</div>
                    <div class="keyword text-lg mb-1">It is this book that I want.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt ɪz ðɪs bʊk ðæt aɪ wʌnt/</div>
                    <div class="text-gray-700">我想要的是这本书。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">It is...that强调时间</div>
                    <div class="keyword text-lg mb-1">It was yesterday that he came.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wʌz ˈjestərdeɪ ðæt hi keɪm/</div>
                    <div class="text-gray-700">他是昨天来的。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">助动词do强调</div>
                    <div class="keyword text-lg mb-1">I do love you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ du lʌv ju/</div>
                    <div class="text-gray-700">我确实爱你。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 8. 练习与应用 -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">8. 练习与应用</h2>

        <!-- 8.1 句型转换练习 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">8.1 句型转换练习</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">陈述句→疑问句</div>
                        <div class="text-base mb-2">She is a teacher.</div>
                        <div class="keyword text-base">Is she a teacher?</div>
                    </div>

                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">肯定句→否定句</div>
                        <div class="text-base mb-2">He likes music.</div>
                        <div class="keyword text-base">He doesn't like music.</div>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">简单句→复合句</div>
                        <div class="text-base mb-2">The weather is nice. We go out.</div>
                        <div class="keyword text-base">Because the weather is nice, we go out.</div>
                    </div>

                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">两个简单句→并列句</div>
                        <div class="text-base mb-2">I study hard. I want to pass the exam.</div>
                        <div class="keyword text-base">I study hard, and I want to pass the exam.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 8.2 实用对话示例 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">8.2 实用对话示例</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-6 shadow-sm">
                    <h4 class="font-semibold mb-3">日常对话</h4>
                    <div class="space-y-2 text-sm">
                        <div><span class="keyword">A:</span> How are you today?</div>
                        <div><span class="keyword">B:</span> I'm fine, thank you. And you?</div>
                        <div><span class="keyword">A:</span> I'm doing well. What are you doing this weekend?</div>
                        <div><span class="keyword">B:</span> I'm going to visit my friends.</div>
                    </div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-6 shadow-sm">
                    <h4 class="font-semibold mb-3">购物对话</h4>
                    <div class="space-y-2 text-sm">
                        <div><span class="keyword">Customer:</span> How much is this shirt?</div>
                        <div><span class="keyword">Sales:</span> It's $25.</div>
                        <div><span class="keyword">Customer:</span> Can I try it on?</div>
                        <div><span class="keyword">Sales:</span> Of course! The fitting room is over there.</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 9. 总结与要点 -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">9. 总结与要点</h2>

        <div class="bg-gray-50 rounded-lg p-6 border">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">掌握基本句型的关键点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <ul class="space-y-3 text-gray-600">
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>理解五种基本句型结构：主谓、主谓宾、主系表、主谓双宾、主谓宾补</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>掌握四种句子类型：陈述句、疑问句、祈使句、感叹句</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>学会使用复合句和并列句表达复杂意思</span>
                    </li>
                </ul>
                <ul class="space-y-3 text-gray-600">
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>注意英语的基本语序SVO，以及特殊情况下的语序变化</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>练习句型转换，提高语言表达的灵活性</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>在实际对话中运用所学句型，增强实用性</span>
                    </li>
                </ul>
            </div>
        </div>
    </section>
</div>